<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Agency;

class AgencyPolicy
{
    /**
     * Determine whether the user can view any agencies.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_agencies')
            || $admin->hasPermissionTo('view_agencies');
    }

    /**
     * Determine whether the user can view the agency.
     */
    public function view(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('manage_agencies')
            || $admin->hasPermissionTo('view_agencies');
    }

    /**
     * Determine whether the user can create agencies.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('manage_agencies')
            || $admin->hasPermissionTo('create_agencies');
    }

    /**
     * Determine whether the user can update the agency.
     */
    public function update(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('manage_agencies')
            || $admin->hasPermissionTo('edit_agencies');
    }

    /**
     * Determine whether the user can delete the agency.
     */
    public function delete(Admin $admin, Agency $agency): bool
    {
        return $admin->hasPermissionTo('manage_agencies')
            || $admin->hasPermissionTo('delete_agencies');
    }
}
